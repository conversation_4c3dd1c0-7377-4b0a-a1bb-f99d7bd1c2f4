# !/usr/bin/python3
# -- coding: utf-8 --
# -------------------------------
# 离线啦论坛
# <AUTHOR>
# @Time 2024.09.16
# @Url https://lixianla.com/
# @Description
# ✨ 功能：
#     离线啦 Steam 账号共享论坛 签到获取经验、金币，可用于论坛权限帖子访问，账号分享可能需要积分才能查看
# ✨ 变量获取：
#     打开 https://lixianla.com/ , 登录账号，按 F12 打开开发者工具，复制任意请求的 cookie 值
#     关键的 cookie 其实是 bbs_sid 和 bbs_token 两个值就可
# ✨ 变量示例：
#     export LXL_COOKIES='bbs_sid=meohxx; bbs_token=3XDeDhxx'，多账号换行分割
# -------------------------------
# cron "33 46 9 * * *" script-path=xxx.py,tag=匹配cron用
# const $ = new Env('离线啦 Steam 账号共享论坛');
from bs4 import BeautifulSoup
from tools.common import BaseRun
#from typing import override
import os
import requests
import ddddocr
import random
import zstandard as zstd
import io

class Run(BaseRun):
    #@override
    def init_vars(self):
        self.session = requests.Session()
        self.session.verify = False
        self.base_url = "https://lixianla.com"
        # 用户信息标记
        self.labels = []
        self.before_info = []
        self.after_info = []
    #@override
    def process_vars(self, info):
        self.cookie = info
        headers = {
            "cache-control": "max-age=0",
            "sec-ch-ua": "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
            "sec-ch-ua-mobile": "?0",
            "sec-ch-ua-platform": "\"Windows\"",
            "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0",
            "accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
            "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "accept-encoding": "zstd",
            "cookie": self.cookie,
            "priority": "u=0, i"
        }
        self.session.headers.update(headers)
    #@override
    def process(self, info, _):
        self.process_vars(info)
        self.get_personal_info()
        self.logger.info("当前用户：{}".format(self.user_name))
        self.logger.info("===>💥 签到")
        self.sign_in()
        self.get_personal_info(end=True)
        msg = self.show_info()
        self.logger.final(msg)

    def parse_response(self, response):
        """
        解析响应内容，支持 zstd 压缩格式

        Args:
            response: requests.Response 对象

        Returns:
            str: 解压后的文本内容
        """
        try:
            # 检查是否是 zstd 编码
            if response.headers.get("Content-Encoding") == "zstd":
                dctx = zstd.ZstdDecompressor()
                # 用 io.BytesIO 包装 response.content 作为解压输入
                with dctx.stream_reader(io.BytesIO(response.content)) as reader:
                    decompressed = reader.read()
                text = decompressed.decode("utf-8")
                self.logger.debug("✅ 成功解析 zstd 压缩响应")
                return text
            else:
                return response.text  # 正常处理非压缩内容
        except Exception as e:
            self.logger.error(f"❌ 解析响应内容失败：{e}")
            # 如果解压失败，尝试直接返回原始文本
            return response.text

    def parse_captcha(self, image_content):
        # 使用OCR识别验证码
        ocr = ddddocr.DdddOcr(beta=True,show_ad=False)
        ocr.set_ranges(5)
        result = ocr.classification(image_content, png_fix=True)
        return result

    # 签到
    def sign_in(self):
        result = self.session.get(self.base_url)
        # 解析主页响应（支持 zstd 压缩）
        text = self.parse_response(result)
        soup = BeautifulSoup(text, "html.parser")
        check_button = soup.select('button[data-modal-title="签到"]')
        check_url_path = check_button[0].attrs["data-modal-url"]
        result = self.session.get(f"{self.base_url}/{check_url_path}")
        # 解析签到页面响应（支持 zstd 压缩）
        text = self.parse_response(result)
        soup = BeautifulSoup(text, "html.parser")
        vcode_img = soup.select('img[title="点击更新"]')
        if len(vcode_img) == 0:
            self.logger.info("ℹ️  已经签到了！！")
            # 关闭推送通知
            self.notify_switch = False
            return
        # 解析验证码
        for _ in range(30):
            sign_headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/137.0.0.0 Safari/537.36 Edg/137.0.0.0",
                "Connection": "keep-alive",
                "Accept": "text/plain, */*; q=0.01",
                "Content-Type": "application/x-www-form-urlencoded",
                "sec-ch-ua-platform": "\"Windows\"",
                "x-requested-with": "XMLHttpRequest",
                "sec-ch-ua": "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
                "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                "sec-ch-ua-mobile": "?0",
                "origin": "https://lixianla.com",
                "sec-fetch-site": "same-origin",
                "sec-fetch-mode": "cors",
                "sec-fetch-dest": "empty",
                "referer": "https://lixianla.com/",
                "accept-language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
                "priority": "u=1, i"
            }
            self.session.headers.update(sign_headers)
            vcode_url_path = f"vcode.htm?{round(random.random(), 14)}"
            png_response = self.session.get(f"{self.base_url}/{vcode_url_path}")
            captcha = self.parse_captcha(png_response.content)
            #只保留数字
            captcha = ''.join(filter(str.isdigit, captcha))
            if len(captcha) != 5:
                self.logger.info("❌ 验证码识别有误，重新识别")
                continue
            data = {
                "vcode": captcha
            }
            result = self.session.post(f"{self.base_url}/{check_url_path}", data=data)
            try:
                res_json = result.json()
            except Exception as e:
                self.logger.error(f"⚠️  响应内容不是 json：{e}")
            if res_json:
                if res_json.get("code") == '0':
                    self.logger.info(f"✅ {res_json.get('message')}")
                elif res_json.get("code") == '-1':
                    self.logger.error(f"⚠️  {res_json.get('message')}")
                    continue
                return
            soup = BeautifulSoup(result.text, "html.parser")
            tag = soup.find('h4', class_='card-title text-center mb-0')
            sign_msg = tag.text.strip() if tag.text else None
            if result.status_code == 200:
                if "成功签到" in sign_msg:
                    self.logger.info(f"✅ {sign_msg}")
                elif "已经签" in sign_msg:
                    self.logger.info(f"ℹ️  {sign_msg}")
                elif "验证码错误" in sign_msg:
                    self.logger.info(f"⚠️  {sign_msg}   重新识别")
                    continue
                return
            else:
                self.logger.error("❌ 签到失败，验证码错误")
        self.logger.error("❌ 签到失败，验证码识别有误")

    def get_personal_info(self, end=False):
        result = self.session.get(f"{self.base_url}/my-credits.htm")
        if result.status_code != 200:
            self.logger.error("❌ 获取用户信息失败")
            exit(0)
        # 解析用户信息页面响应（支持 zstd 压缩）
        text = self.parse_response(result)
        soup = BeautifulSoup(text, "html.parser")
        self.user_name = soup.find("span", class_='h4 font-weight-bold').text.strip()
        info_divs = soup.find_all("div", class_='input-group mb-3')
        for info in info_divs:
            value = info.find("input", type='text')['value']
            if not end:
                label = info.find("span", class_='input-group-text').text.strip()
                self.labels.append(label)
                self.before_info.append(value)
            else:
                self.after_info.append(value)
    def show_info(self):
        msg = ""
        for index, label in enumerate(self.labels):
            msg += f"{label}: {self.before_info[index]} -> {self.after_info[index]}\n"
        return msg.strip()
if __name__ == '__main__':
    app_name = "离线啦 Steam 账号共享论坛"
    app_env_name = "LXL_COOKIES"
    local_script_name = os.path.basename(__file__)
    local_version = '2025.06.14'
    run = Run(app_name=app_name,
              app_env_name=app_env_name,
              script_version=local_version)
    run.main()

